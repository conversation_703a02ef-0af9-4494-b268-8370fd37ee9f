from typing import Dict, List, Any, Optional
import time
from datetime import datetime

from config import Config
from language_detector import LanguageDetector
from vector_store import VectorStore
from llm_client import LLMClient

class ThaiChatbot:
    def __init__(self):
        self.config = Config()
        self.language_detector = LanguageDetector()
        self.vector_store = VectorStore()
        self.llm_client = LLMClient()
        self.conversation_history = []
        
        print("Thai Chatbot initialized successfully!")
    
    def process_message(
        self, 
        user_message: str, 
        use_rag: bool = True,
        language_preference: str = 'auto'
    ) -> Dict[str, Any]:
        """
        Process a user message and generate a response
        
        Args:
            user_message (str): The user's input message
            use_rag (bool): Whether to use RAG for context retrieval
            language_preference (str): Language preference ('auto', 'en', 'th')
            
        Returns:
            Dict containing response and metadata
        """
        start_time = time.time()
        
        try:
            # Step 1: Detect language
            language_info = self._detect_language(user_message, language_preference)
            
            # Step 2: Identify user needs (optional enhancement)
            needs_analysis = self.llm_client.identify_user_needs(
                user_message, 
                language_info['code']
            )
            
            # Step 3: Retrieve relevant context if RAG is enabled
            context = []
            if use_rag:
                context = self._retrieve_context(user_message, language_info['code'])
            
            # Step 4: Generate response using LLM
            response_data = self.llm_client.generate_response(
                user_message=user_message,
                language=language_info['code'],
                context=context,
                conversation_history=self.conversation_history[-10:],  # Last 10 messages
                system_prompt_type='general'
            )
            
            # Step 5: Update conversation history
            self._update_conversation_history(user_message, response_data['response'])
            
            # Step 6: Prepare comprehensive response
            processing_time = time.time() - start_time
            
            result = {
                'response': response_data['response'],
                'language_detected': language_info,
                'needs_analysis': needs_analysis,
                'context_used': len(context),
                'context_items': context,
                'tokens_used': response_data.get('tokens_used', 0),
                'model_used': response_data.get('model', self.config.OPENAI_MODEL),
                'processing_time': round(processing_time, 2),
                'timestamp': datetime.now().isoformat(),
                'rag_enabled': use_rag
            }
            
            # Add error information if present
            if 'error' in response_data:
                result['error'] = response_data['error']
            
            return result
            
        except Exception as e:
            return self._handle_error(e, user_message, language_preference)
    
    def _detect_language(self, text: str, preference: str = 'auto') -> Dict[str, Any]:
        """Detect the language of the input text"""
        if preference != 'auto':
            # Use preferred language
            return {
                'code': preference,
                'name': 'English' if preference == 'en' else 'Thai',
                'confidence': 1.0,
                'is_supported': True,
                'detection_method': 'preference'
            }
        
        # Auto-detect language
        detection_result = self.language_detector.detect_language(text)
        detection_result['detection_method'] = 'auto'
        
        # Fallback to English if language is not supported
        if not detection_result['is_supported']:
            detection_result = {
                'code': 'en',
                'name': 'English',
                'confidence': 0.5,
                'is_supported': True,
                'detection_method': 'fallback'
            }
        
        return detection_result
    
    def _retrieve_context(self, query: str, language: str) -> List[Dict[str, Any]]:
        """Retrieve relevant context from the vector store"""
        try:
            # Search for relevant documents
            if language in ['en', 'th']:
                # Search in specific language first
                results = self.vector_store.search_by_language(
                    query=query,
                    language=language,
                    top_k=self.config.TOP_K_RESULTS
                )
                
                # If not enough results, search in all languages
                if len(results) < 2:
                    additional_results = self.vector_store.search(
                        query=query,
                        top_k=self.config.TOP_K_RESULTS - len(results)
                    )
                    results.extend(additional_results)
            else:
                # Search in all languages
                results = self.vector_store.search(
                    query=query,
                    top_k=self.config.TOP_K_RESULTS
                )
            
            return results
            
        except Exception as e:
            print(f"Error retrieving context: {e}")
            return []
    
    def _update_conversation_history(self, user_message: str, assistant_response: str):
        """Update the conversation history"""
        # Add user message
        self.conversation_history.append({
            "role": "user",
            "content": user_message
        })
        
        # Add assistant response
        self.conversation_history.append({
            "role": "assistant", 
            "content": assistant_response
        })
        
        # Keep only the last 20 messages (10 exchanges)
        if len(self.conversation_history) > 20:
            self.conversation_history = self.conversation_history[-20:]
    
    def _handle_error(self, error: Exception, user_message: str, language_preference: str) -> Dict[str, Any]:
        """Handle errors gracefully"""
        error_message = str(error)
        
        # Determine response language
        if language_preference == 'th':
            response = "ขออภัย เกิดข้อผิดพลาดในการประมวลผลคำถามของคุณ กรุณาลองใหม่อีกครั้ง"
        else:
            response = "I apologize, but I encountered an error processing your question. Please try again."
        
        return {
            'response': response,
            'error': error_message,
            'language_detected': {
                'code': language_preference if language_preference != 'auto' else 'en',
                'name': 'Thai' if language_preference == 'th' else 'English',
                'confidence': 0.0,
                'is_supported': True,
                'detection_method': 'error_fallback'
            },
            'needs_analysis': {'category': 'error', 'analysis': 'Error occurred'},
            'context_used': 0,
            'context_items': [],
            'tokens_used': 0,
            'model_used': self.config.OPENAI_MODEL,
            'processing_time': 0,
            'timestamp': datetime.now().isoformat(),
            'rag_enabled': False
        }
    
    def add_knowledge(self, text: str, metadata: Dict[str, Any]) -> str:
        """
        Add knowledge to the vector store
        
        Args:
            text (str): The text content to add
            metadata (dict): Metadata including language, category, etc.
            
        Returns:
            str: Document ID
        """
        try:
            doc_id = self.vector_store.add_document(text, metadata)
            return doc_id
        except Exception as e:
            print(f"Error adding knowledge: {e}")
            return ""
    
    def add_knowledge_batch(self, documents: List[Dict[str, Any]]) -> List[str]:
        """
        Add multiple knowledge documents in batch
        
        Args:
            documents: List of dicts with 'text' and 'metadata' keys
            
        Returns:
            List of document IDs
        """
        try:
            doc_ids = self.vector_store.add_documents_batch(documents)
            return doc_ids
        except Exception as e:
            print(f"Error adding knowledge batch: {e}")
            return []
    
    def get_conversation_summary(self) -> Dict[str, Any]:
        """Get a summary of the current conversation"""
        if not self.conversation_history:
            return {
                'total_exchanges': 0,
                'languages_used': [],
                'topics_discussed': []
            }
        
        # Count exchanges (pairs of user/assistant messages)
        total_exchanges = len(self.conversation_history) // 2
        
        # Extract some basic statistics
        user_messages = [msg['content'] for msg in self.conversation_history if msg['role'] == 'user']
        
        return {
            'total_exchanges': total_exchanges,
            'total_messages': len(self.conversation_history),
            'recent_topics': user_messages[-3:] if user_messages else [],
            'conversation_length': sum(len(msg['content']) for msg in self.conversation_history)
        }
    
    def clear_conversation(self):
        """Clear the conversation history"""
        self.conversation_history = []
    
    def get_vector_store_stats(self) -> Dict[str, Any]:
        """Get statistics about the vector store"""
        return self.vector_store.get_index_stats()
