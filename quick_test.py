#!/usr/bin/env python3
"""
Quick test script to verify the Thai-English Chatbot functionality
"""

from chatbot import ThaiChatbot
import time

def test_chatbot_responses():
    """Test the chatbot with various inputs"""
    print("🤖 Testing Thai-English Chatbot...")
    print("=" * 50)
    
    # Initialize chatbot
    chatbot = ThaiChatbot()
    
    # Test cases
    test_cases = [
        {
            "input": "What is the capital of Thailand?",
            "language": "en",
            "description": "English question about Thailand"
        },
        {
            "input": "เมืองหลวงของไทยคืออะไร",
            "language": "th", 
            "description": "Thai question about Thailand's capital"
        },
        {
            "input": "Tell me about Thai food",
            "language": "en",
            "description": "English question about Thai cuisine"
        },
        {
            "input": "อาหารไทยมีอะไรบ้าง",
            "language": "th",
            "description": "Thai question about Thai food"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}: {test_case['description']}")
        print(f"Input: {test_case['input']}")
        
        start_time = time.time()
        
        try:
            response = chatbot.process_message(
                user_message=test_case['input'],
                use_rag=True,
                language_preference="auto"
            )
            
            end_time = time.time()
            
            print(f"✅ Response: {response['response'][:100]}...")
            print(f"🌐 Detected Language: {response['language_detected']['name']} ({response['language_detected']['confidence']:.1%})")
            print(f"📚 Context Used: {response['context_used']} documents")
            print(f"⏱️  Response Time: {end_time - start_time:.2f}s")
            print(f"🔢 Tokens Used: {response['tokens_used']}")
            
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print("-" * 30)
    
    print("\n🎉 Testing completed!")

if __name__ == "__main__":
    test_chatbot_responses()
