import{r as l,M as u,j as d,bp as x,c5 as V}from"./index.C1NIn1Y2.js";import{a as h}from"./useBasicWidgetState.Ci89jaH5.js";import"./FormClearHelper.D1M9GM_c.js";const U=(t,o)=>t.getStringValue(o),v=t=>t.options.length===0||u(t.default)?null:t.options[t.default],C=t=>t.rawValue??null,F=(t,o,e,a)=>{o.setStringValue(t,e.value,{fromUi:e.fromUi},a)},M=({disabled:t,element:o,widgetMgr:e,fragmentId:a})=>{const{options:n,help:c,label:i,labelVisibility:r,placeholder:p,acceptNewOptions:g}=o,[f,s]=h({getStateFromWidgetMgr:U,getDefaultStateFromProto:v,getCurrStateFromProto:C,updateWidgetMgrState:F,element:o,widgetMgr:e,fragmentId:a}),S=l.useCallback(b=>{s({value:b,fromUi:!0})},[s]),m=u(o.default)&&!t;return d(V,{label:i,labelVisibility:x(r==null?void 0:r.value),options:n,disabled:t,onChange:S,value:f,help:c,placeholder:p,clearable:m,acceptNewOptions:g})},w=l.memo(M);export{w as default};
