import{n as C,r,z as W,b6 as I,b$ as V,c0 as $,C as O,j as l,bp as H,bD as L,bq as E,b7 as N,br as P,b_ as k,c1 as A,bW as B,c2 as F,bA as D}from"./index.C1NIn1Y2.js";import{a as U}from"./useBasicWidgetState.Ci89jaH5.js";import"./FormClearHelper.D1M9GM_c.js";const j=C("div",{target:"euzcfsp0"})(({theme:s})=>({"span[aria-disabled='true']":{background:s.colors.fadedText05}})),G=(s,t)=>s.getStringArrayValue(t),K=s=>s.default.map(t=>s.options[t])??null,Y=s=>s.rawValues??null,_=(s,t,a,p)=>{t.setStringArrayValue(s,a.value,{fromUi:a.fromUi},p)},q=s=>{var S;const{element:t,widgetMgr:a,fragmentId:p}=s,e=W(),f=r.useContext(I),[i,m]=U({getStateFromWidgetMgr:G,getDefaultStateFromProto:K,getCurrStateFromProto:Y,updateWidgetMgrState:_,element:t,widgetMgr:a,fragmentId:p}),h=t.maxSelections>0&&i.length>=t.maxSelections,y=r.useMemo(()=>{if(t.maxSelections===0)return"No results";if(i.length===t.maxSelections){const o=t.maxSelections!==1?"options":"option";return`You can only select up to ${t.maxSelections} ${o}. Remove an option first.`}return"No results"},[t.maxSelections,i.length]),x=r.useMemo(()=>i.map(o=>({value:o,label:o})),[i]),b=r.useCallback(o=>{var n,d;switch(o.type){case"remove":return V(i,(n=o.option)==null?void 0:n.value);case"clear":return[];case"select":return i.concat([(d=o.option)==null?void 0:d.value]);default:throw new Error(`State transition is unknown: ${o.type}`)}},[i]),v=r.useCallback(o=>{t.maxSelections&&o.type==="select"&&i.length>=t.maxSelections||m({value:b(o),fromUi:!0})},[t.maxSelections,b,m,i.length]),z=r.useCallback((o,n)=>{if(h)return[];const d=o.filter(T=>!i.includes(T.value));return $(d,n)},[h,i]),{options:u}=t;let c=s.disabled,g=t.placeholder;u.length===0&&(t.acceptNewOptions?g="Add options":(g="No options to select",c=!0));const w=u.map((o,n)=>({label:o,value:o,id:`${o}_${n}`})),M=u.length>10,R=r.useMemo(()=>{const o=e.fontSizes.baseFontSize*1.6+14;return`${Math.round(o*4.25)}px`},[e.fontSizes.baseFontSize]);return O("div",{className:"stMultiSelect","data-testid":"stMultiSelect",children:[l(P,{label:t.label,disabled:c,labelVisibility:H((S=t.labelVisibility)==null?void 0:S.value),children:t.help&&l(L,{children:l(E,{content:t.help,placement:N.TOP_RIGHT})})}),l(j,{children:l(k,{creatable:t.acceptNewOptions??!1,options:w,labelKey:"label",valueKey:"value","aria-label":t.label,placeholder:g,type:F.select,multi:!0,onChange:v,value:x,disabled:c,size:"compact",noResultsMsg:y,filterOptions:z,closeOnSelect:!1,ignoreCase:!1,overrides:{Popover:{props:{ignoreBoundary:f,overrides:{Body:{style:()=>({marginTop:e.spacing.px})}}}},SelectArrow:{component:B,props:{style:{cursor:"pointer"},overrides:{Svg:{style:()=>({width:e.iconSizes.xl,height:e.iconSizes.xl})}}}},IconsContainer:{style:()=>({paddingRight:e.spacing.sm})},ControlContainer:{style:{maxHeight:R,minHeight:e.sizes.minElementHeight,borderLeftWidth:e.sizes.borderWidth,borderRightWidth:e.sizes.borderWidth,borderTopWidth:e.sizes.borderWidth,borderBottomWidth:e.sizes.borderWidth}},Placeholder:{style:()=>({flex:"inherit",color:c?e.colors.fadedText40:e.colors.fadedText60})},ValueContainer:{style:()=>({overflowY:"auto",paddingLeft:e.spacing.sm,paddingTop:e.spacing.none,paddingBottom:e.spacing.none,paddingRight:e.spacing.none})},ClearIcon:{props:{overrides:{Svg:{style:{color:e.colors.darkGray,padding:e.spacing.threeXS,height:e.sizes.clearIconSize,width:e.sizes.clearIconSize,cursor:"pointer",":hover":{fill:e.colors.bodyText}}}}}},SearchIcon:{style:{color:e.colors.darkGray}},Tag:{props:{overrides:{Root:{style:{borderTopLeftRadius:e.radii.md,borderTopRightRadius:e.radii.md,borderBottomRightRadius:e.radii.md,borderBottomLeftRadius:e.radii.md,fontSize:e.fontSizes.md,paddingLeft:e.spacing.sm,marginLeft:e.spacing.none,marginRight:e.spacing.sm,height:`calc(${e.sizes.minElementHeight} - 2 * ${e.spacing.xs})`,maxWidth:`calc(100% - ${e.spacing.lg})`,cursor:"default !important"}},Action:{style:{paddingLeft:0}},ActionIcon:{props:{overrides:{Svg:{style:{width:"0.625em",height:"0.625em"}}}}}}}},MultiValue:{props:{overrides:{Root:{style:{fontSize:e.fontSizes.sm}}}}},Input:{props:{readOnly:D.isMobile&&M===!1?"readonly":null}},Dropdown:{component:A}}})})]})},Z=r.memo(q);export{Z as default};
