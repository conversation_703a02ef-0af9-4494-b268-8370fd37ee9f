import{n as o,bk as S,i as f,r as l,j as r,C as g,c3 as u,aC as $}from"./index.C1NIn1Y2.js";const m=o(S,{shouldForwardProp:f,target:"e12gfcky0"})(({theme:e})=>({fontSize:e.fontSizes.sm,width:e.sizes.spinnerSize,height:e.sizes.spinnerSize,borderWidth:e.sizes.spinnerThickness,justifyContents:"center",padding:e.spacing.none,margin:e.spacing.none,borderColor:e.colors.borderColor,borderTopColor:e.colors.secondary,flexGrow:0,flexShrink:0})),x=o("div",{target:"e12gfcky1"})(({theme:e,cache:s})=>({...s?{paddingBottom:e.spacing.lg,background:`linear-gradient(to bottom, ${e.colors.bgColor} 0%, ${e.colors.bgColor} 80%, transparent 100%)`}:null})),y=o("div",{target:"e12gfcky2"})(({theme:e})=>({opacity:.6,fontSize:e.fontSizes.sm})),T=o("div",{target:"e12gfcky3"})(({theme:e})=>({display:"flex",gap:e.spacing.sm,alignItems:"center",width:"100%"})),h=e=>{const s=Math.floor(e/3600),n=Math.floor(e%3600/60),t=e%60;if(s===0&&n===0)return`(${t.toFixed(1)} seconds)`;if(s===0){const d=`${n} minute${n===1?"":"s"}`,p=t===0?"":`, ${t.toFixed(1)} seconds`;return`(${d}${p})`}const i=`${s} hour${s===1?"":"s"}`,a=n===0?"":`, ${n} minute${n===1?"":"s"}`,c=t===0?"":`, ${t.toFixed(1)} seconds`;return`(${i}${a}${c})`};function C({element:e}){const{cache:s,showTime:n}=e,[t,i]=l.useState(0);return l.useEffect(()=>{if(!n)return;const a=setInterval(()=>{i(c=>c+.1)},100);return()=>clearInterval(a)},[n]),r(x,{className:u({stSpinner:!0,stCacheSpinner:s}),"data-testid":"stSpinner",cache:s,children:g(T,{children:[r(m,{}),r($,{source:e.text,allowHTML:!1}),n&&r(y,{children:h(t)})]})})}const k=l.memo(C);export{k as default};
