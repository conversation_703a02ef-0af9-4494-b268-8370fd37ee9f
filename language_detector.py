from langdetect import detect, DetectorFactory
from langdetect.lang_detect_exception import LangDetectException
import re

# Set seed for consistent results
DetectorFactory.seed = 0

class LanguageDetector:
    def __init__(self):
        self.supported_languages = {
            'en': 'English',
            'th': 'Thai'
        }
    
    def detect_language(self, text: str) -> dict:
        """
        Detect the language of the input text
        
        Args:
            text (str): Input text to analyze
            
        Returns:
            dict: Dictionary containing language code, name, and confidence
        """
        if not text or not text.strip():
            return {
                'code': 'unknown',
                'name': 'Unknown',
                'confidence': 0.0,
                'is_supported': False
            }
        
        # Clean text for better detection
        cleaned_text = self._clean_text(text)
        
        try:
            # Detect language
            detected_lang = detect(cleaned_text)
            
            # Check if it's a supported language
            is_supported = detected_lang in self.supported_languages
            lang_name = self.supported_languages.get(detected_lang, detected_lang.upper())
            
            # Simple confidence estimation based on text characteristics
            confidence = self._estimate_confidence(cleaned_text, detected_lang)
            
            return {
                'code': detected_lang,
                'name': lang_name,
                'confidence': confidence,
                'is_supported': is_supported
            }
            
        except LangDetectException:
            # Fallback: try to detect based on character patterns
            return self._fallback_detection(cleaned_text)
    
    def _clean_text(self, text: str) -> str:
        """Clean text for better language detection"""
        # Remove URLs, emails, and special characters that might interfere
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        text = re.sub(r'\S+@\S+', '', text)
        text = re.sub(r'[^\w\s\u0E00-\u0E7F]', ' ', text)  # Keep Thai characters
        return text.strip()
    
    def _estimate_confidence(self, text: str, detected_lang: str) -> float:
        """Estimate confidence based on text characteristics"""
        if len(text) < 10:
            return 0.6  # Lower confidence for short texts
        
        if detected_lang == 'th':
            # Check for Thai characters
            thai_chars = len(re.findall(r'[\u0E00-\u0E7F]', text))
            total_chars = len(re.findall(r'[a-zA-Z\u0E00-\u0E7F]', text))
            if total_chars > 0:
                thai_ratio = thai_chars / total_chars
                return min(0.9, 0.5 + thai_ratio * 0.4)
        
        elif detected_lang == 'en':
            # Check for English characters
            english_chars = len(re.findall(r'[a-zA-Z]', text))
            total_chars = len(re.findall(r'[a-zA-Z\u0E00-\u0E7F]', text))
            if total_chars > 0:
                english_ratio = english_chars / total_chars
                return min(0.9, 0.5 + english_ratio * 0.4)
        
        return 0.7  # Default confidence
    
    def _fallback_detection(self, text: str) -> dict:
        """Fallback detection based on character patterns"""
        # Count Thai and English characters
        thai_chars = len(re.findall(r'[\u0E00-\u0E7F]', text))
        english_chars = len(re.findall(r'[a-zA-Z]', text))
        
        if thai_chars > english_chars:
            return {
                'code': 'th',
                'name': 'Thai',
                'confidence': 0.6,
                'is_supported': True
            }
        elif english_chars > 0:
            return {
                'code': 'en',
                'name': 'English',
                'confidence': 0.6,
                'is_supported': True
            }
        else:
            return {
                'code': 'unknown',
                'name': 'Unknown',
                'confidence': 0.0,
                'is_supported': False
            }
    
    def is_mixed_language(self, text: str) -> bool:
        """Check if text contains mixed languages"""
        thai_chars = len(re.findall(r'[\u0E00-\u0E7F]', text))
        english_chars = len(re.findall(r'[a-zA-Z]', text))
        
        # Consider it mixed if both languages have significant presence
        total_chars = thai_chars + english_chars
        if total_chars == 0:
            return False
        
        thai_ratio = thai_chars / total_chars
        english_ratio = english_chars / total_chars
        
        # Mixed if both languages have at least 20% presence
        return thai_ratio >= 0.2 and english_ratio >= 0.2
