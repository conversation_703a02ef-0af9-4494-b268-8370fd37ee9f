#!/usr/bin/env python3
"""
Test script for Thai-English Chatbot
This script runs basic tests to ensure all components are working correctly
"""

import sys
import time
from typing import List, Dict, Any

def test_imports():
    """Test if all required modules can be imported"""
    print("🧪 Testing imports...")
    
    try:
        from config import Config
        from language_detector import LanguageDetector
        from vector_store import VectorStore
        from llm_client import LLMClient
        from chatbot import ThaiChatbot
        print("✅ All imports successful")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_configuration():
    """Test configuration setup"""
    print("🔧 Testing configuration...")
    
    try:
        from config import Config
        Config.validate_config()
        print("✅ Configuration is valid")
        return True
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def test_language_detection():
    """Test language detection functionality"""
    print("🌐 Testing language detection...")
    
    try:
        from language_detector import LanguageDetector
        detector = LanguageDetector()
        
        # Test English
        result_en = detector.detect_language("Hello, how are you today?")
        assert result_en['code'] == 'en', f"Expected 'en', got '{result_en['code']}'"
        
        # Test Thai
        result_th = detector.detect_language("สวัสดีครับ คุณสบายดีไหม")
        assert result_th['code'] == 'th', f"Expected 'th', got '{result_th['code']}'"
        
        # Test mixed language detection
        is_mixed = detector.is_mixed_language("Hello สวัสดี")
        assert is_mixed == True, "Mixed language detection failed"
        
        print("✅ Language detection working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Language detection error: {e}")
        return False

def test_vector_store():
    """Test vector store functionality"""
    print("🗄️ Testing vector store...")
    
    try:
        from vector_store import VectorStore
        vector_store = VectorStore()
        
        # Test embedding creation
        embedding = vector_store.create_embedding("Test text for embedding")
        assert len(embedding) == vector_store.config.EMBEDDING_DIMENSION, "Embedding dimension mismatch"
        
        # Test document addition
        doc_id = vector_store.add_document(
            "This is a test document for the chatbot.",
            {"language": "en", "category": "test", "source": "unit_test"}
        )
        assert doc_id, "Failed to add document"
        
        # Test search
        results = vector_store.search("test document", top_k=1)
        assert len(results) >= 0, "Search failed"
        
        # Clean up test document
        vector_store.delete_document(doc_id)
        
        print("✅ Vector store working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Vector store error: {e}")
        return False

def test_llm_client():
    """Test LLM client functionality"""
    print("🧠 Testing LLM client...")
    
    try:
        from llm_client import LLMClient
        llm_client = LLMClient()
        
        # Test simple response generation
        response = llm_client.generate_response(
            user_message="Hello, can you help me?",
            language="en"
        )
        
        assert 'response' in response, "No response generated"
        assert response['response'], "Empty response"
        assert 'tokens_used' in response, "Token count missing"
        
        print("✅ LLM client working correctly")
        return True
        
    except Exception as e:
        print(f"❌ LLM client error: {e}")
        return False

def test_chatbot_integration():
    """Test full chatbot integration"""
    print("🤖 Testing chatbot integration...")
    
    try:
        from chatbot import ThaiChatbot
        chatbot = ThaiChatbot()
        
        # Test English message
        response_en = chatbot.process_message(
            user_message="What is the capital of Thailand?",
            use_rag=False,
            language_preference="en"
        )
        
        assert 'response' in response_en, "No response from chatbot"
        assert response_en['language_detected']['code'] == 'en', "Language detection failed"
        
        # Test Thai message
        response_th = chatbot.process_message(
            user_message="เมืองหลวงของไทยคืออะไร",
            use_rag=False,
            language_preference="th"
        )
        
        assert 'response' in response_th, "No response from chatbot"
        assert response_th['language_detected']['code'] == 'th', "Thai language detection failed"
        
        print("✅ Chatbot integration working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Chatbot integration error: {e}")
        return False

def test_rag_functionality():
    """Test RAG (Retrieval-Augmented Generation) functionality"""
    print("📚 Testing RAG functionality...")
    
    try:
        from chatbot import ThaiChatbot
        chatbot = ThaiChatbot()
        
        # Add a test document
        doc_id = chatbot.add_knowledge(
            "Bangkok is the capital city of Thailand and has a population of over 8 million people.",
            {"language": "en", "category": "geography", "source": "test"}
        )
        
        assert doc_id, "Failed to add knowledge"
        
        # Test RAG-enabled query
        response = chatbot.process_message(
            user_message="What is the population of Bangkok?",
            use_rag=True,
            language_preference="en"
        )
        
        assert 'response' in response, "No RAG response"
        assert response['context_used'] >= 0, "Context usage not tracked"
        
        print("✅ RAG functionality working correctly")
        return True
        
    except Exception as e:
        print(f"❌ RAG functionality error: {e}")
        return False

def run_performance_test():
    """Run basic performance tests"""
    print("⚡ Running performance tests...")
    
    try:
        from chatbot import ThaiChatbot
        chatbot = ThaiChatbot()
        
        # Test response time
        start_time = time.time()
        response = chatbot.process_message(
            user_message="Hello, this is a performance test.",
            use_rag=False,
            language_preference="en"
        )
        end_time = time.time()
        
        response_time = end_time - start_time
        print(f"Response time: {response_time:.2f} seconds")
        
        if response_time > 30:  # 30 seconds threshold
            print("⚠️  Response time is quite slow")
        else:
            print("✅ Response time is acceptable")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance test error: {e}")
        return False

def main():
    """Main test runner"""
    print("🚀 Running Thai-English Chatbot Tests...")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Configuration Test", test_configuration),
        ("Language Detection Test", test_language_detection),
        ("Vector Store Test", test_vector_store),
        ("LLM Client Test", test_llm_client),
        ("Chatbot Integration Test", test_chatbot_integration),
        ("RAG Functionality Test", test_rag_functionality),
        ("Performance Test", run_performance_test),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{test_name}...")
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! The chatbot is ready to use.")
        print("\nTo run the application:")
        print("streamlit run streamlit_app.py")
    else:
        print("❌ Some tests failed. Please check the configuration and dependencies.")
        sys.exit(1)

if __name__ == "__main__":
    main()
