import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    # OpenAI Configuration
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    OPENAI_MODEL = "gpt-3.5-turbo"
    
    # Pinecone Configuration
    PINECONE_API_KEY = os.getenv("PINECONE_API_KEY")
    PINECONE_ENVIRONMENT = os.getenv("PINECONE_ENVIRONMENT")
    PINECONE_INDEX_NAME = os.getenv("PINECONE_INDEX_NAME", "thai-chatbot-index")
    
    # Embedding Configuration
    EMBEDDING_MODEL = "all-MiniLM-L6-v2"
    EMBEDDING_DIMENSION = 384
    
    # App Configuration
    APP_TITLE = os.getenv("APP_TITLE", "Thai-English Chatbot")
    DEFAULT_LANGUAGE = os.getenv("DEFAULT_LANGUAGE", "auto")
    MAX_TOKENS = 1000
    TEMPERATURE = 0.7
    
    # RAG Configuration
    TOP_K_RESULTS = 5
    SIMILARITY_THRESHOLD = 0.7
    
    @classmethod
    def validate_config(cls):
        """Validate that all required configuration is present"""
        required_vars = [
            "OPENAI_API_KEY",
            "PINECONE_API_KEY", 
            "PINECONE_ENVIRONMENT"
        ]
        
        missing_vars = []
        for var in required_vars:
            if not getattr(cls, var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
        
        return True
