#!/usr/bin/env python3
"""
Data loader for Thai-English Chatbot
This script loads sample data into the Pinecone vector database
"""

import json
from typing import List, Dict, Any
from chatbot import ThaiChatbot

class DataLoader:
    def __init__(self):
        self.chatbot = ThaiChatbot()
    
    def load_sample_data(self) -> bool:
        """Load sample data into the vector database"""
        sample_data = self.get_sample_data()
        
        try:
            print(f"Loading {len(sample_data)} sample documents...")
            doc_ids = self.chatbot.add_knowledge_batch(sample_data)
            
            if doc_ids:
                print(f"✅ Successfully loaded {len(doc_ids)} documents")
                return True
            else:
                print("❌ Failed to load documents")
                return False
                
        except Exception as e:
            print(f"❌ Error loading sample data: {e}")
            return False
    
    def get_sample_data(self) -> List[Dict[str, Any]]:
        """Get sample data for the knowledge base"""
        return [
            # Geography - English
            {
                "text": "Bangkok is the capital and largest city of Thailand, located in the Chao Phraya River delta in central Thailand. It has a population of over 8 million people and is known for its vibrant street life, cultural landmarks, and delicious street food.",
                "metadata": {
                    "language": "en",
                    "category": "geography",
                    "topic": "bangkok",
                    "source": "sample_data"
                }
            },
            
            # Geography - Thai
            {
                "text": "กรุงเทพมหานครเป็นเมืองหลวงและเมืองที่ใหญ่ที่สุดของประเทศไทย ตั้งอยู่ในสามเหลี่ยมปากแม่น้ำเจ้าพระยาในภาคกลางของไทย มีประชากรมากกว่า 8 ล้านคน และมีชื่อเสียงในเรื่องวิถีชีวิตที่คึกคัก สถานที่สำคัญทางวัฒนธรรม และอาหารริมทางที่อร่อย",
                "metadata": {
                    "language": "th",
                    "category": "geography", 
                    "topic": "bangkok",
                    "source": "sample_data"
                }
            },
            
            # Food - English
            {
                "text": "Thai cuisine is characterized by its balance of sweet, sour, salty, and spicy flavors. Popular dishes include Pad Thai (stir-fried noodles), Tom Yum soup (spicy and sour soup), Green Curry, Massaman Curry, and Mango Sticky Rice for dessert.",
                "metadata": {
                    "language": "en",
                    "category": "food",
                    "topic": "thai_cuisine",
                    "source": "sample_data"
                }
            },
            
            # Food - Thai  
            {
                "text": "อาหารไทยมีเอกลักษณ์ที่การผสมผสานรสหวาน เปรียว เค็ม และเผ็ด อาหารยอดนิยม ได้แก่ ผัดไทย ต้มยำ แกงเขียวหวาน แกงมัสมั่น และข้าวเหนียวมะม่วงสำหรับของหวาน",
                "metadata": {
                    "language": "th",
                    "category": "food",
                    "topic": "thai_cuisine", 
                    "source": "sample_data"
                }
            },
            
            # Culture - English
            {
                "text": "Thai culture is deeply influenced by Buddhism, which is practiced by about 95% of the population. Important cultural values include respect for elders, the concept of 'sanuk' (fun), 'kreng jai' (consideration for others), and the traditional greeting 'wai' with palms pressed together.",
                "metadata": {
                    "language": "en",
                    "category": "culture",
                    "topic": "thai_culture",
                    "source": "sample_data"
                }
            },
            
            # Culture - Thai
            {
                "text": "วัฒนธรรมไทยได้รับอิทธิพลอย่างลึกซึ้งจากพุทธศาสนา ซึ่งประชากรประมาณ 95% นับถือ ค่านิยมทางวัฒนธรรมที่สำคัญ ได้แก่ การเคารพผู้ใหญ่ แนวคิดเรื่อง 'สนุก' 'เกรงใจ' และการทักทายแบบไทยด้วยการ 'ไว้'",
                "metadata": {
                    "language": "th",
                    "category": "culture",
                    "topic": "thai_culture",
                    "source": "sample_data"
                }
            },
            
            # Travel - English
            {
                "text": "Popular tourist destinations in Thailand include the beaches of Phuket and Koh Samui, the ancient city of Ayutthaya, the mountain town of Chiang Mai, and the floating markets near Bangkok. The best time to visit is during the cool season from November to February.",
                "metadata": {
                    "language": "en",
                    "category": "travel",
                    "topic": "thailand_tourism",
                    "source": "sample_data"
                }
            },
            
            # Travel - Thai
            {
                "text": "สถานที่ท่องเที่ยวยอดนิยมในประเทศไทย ได้แก่ ชายหาดภูเก็ตและเกาะสมุย เมืองโบราณอยุธยา เมืองบนดอยเชียงใหม่ และตลาดน้ำใกล้กรุงเทพฯ ช่วงเวลาที่ดีที่สุดในการเที่ยวคือฤดูหนาวตั้งแต่เดือนพฤศจิกายนถึงกุมภาพันธ์",
                "metadata": {
                    "language": "th",
                    "category": "travel",
                    "topic": "thailand_tourism",
                    "source": "sample_data"
                }
            },
            
            # Language - English
            {
                "text": "The Thai language belongs to the Tai-Kadai language family and is the official language of Thailand. It has its own unique script with 44 consonants and 15 vowel symbols. Thai is a tonal language with five tones that can change the meaning of words.",
                "metadata": {
                    "language": "en",
                    "category": "language",
                    "topic": "thai_language",
                    "source": "sample_data"
                }
            },
            
            # Language - Thai
            {
                "text": "ภาษาไทยเป็นภาษาในตระกูลไท-กะได และเป็นภาษาราชการของประเทศไทย มีอักษรเฉพาะตัวที่มีพยัญชนะ 44 ตัว และสระ 15 รูป ภาษาไทยเป็นภาษาวรรณยุกต์ที่มี 5 เสียงวรรณยุกต์ที่สามารถเปลี่ยนความหมายของคำได้",
                "metadata": {
                    "language": "th",
                    "category": "language",
                    "topic": "thai_language",
                    "source": "sample_data"
                }
            },
            
            # Technology - English
            {
                "text": "Thailand is becoming a major tech hub in Southeast Asia. Bangkok has a growing startup ecosystem, and the government is promoting digital transformation through initiatives like Thailand 4.0. Popular Thai tech companies include Grab, Line, and CP Group.",
                "metadata": {
                    "language": "en",
                    "category": "technology",
                    "topic": "thai_tech",
                    "source": "sample_data"
                }
            },
            
            # Technology - Thai
            {
                "text": "ประเทศไทยกำลังกลายเป็นศูนย์กลางเทคโนโลยีที่สำคัญในเอเชียตะวันออกเฉียงใต้ กรุงเทพฯ มีระบบนิเวศสตาร์ทอัพที่เติบโต และรัฐบาลส่งเสริมการเปลี่ยนแปลงดิจิทัลผ่านโครงการต่างๆ เช่น ไทยแลนด์ 4.0 บริษัทเทคโนโลยีไทยที่มีชื่อเสียง ได้แก่ แกร็บ ไลน์ และกลุมซีพี",
                "metadata": {
                    "language": "th",
                    "category": "technology",
                    "topic": "thai_tech",
                    "source": "sample_data"
                }
            }
        ]
    
    def load_from_file(self, file_path: str) -> bool:
        """Load data from a JSON file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"Loading {len(data)} documents from {file_path}...")
            doc_ids = self.chatbot.add_knowledge_batch(data)
            
            if doc_ids:
                print(f"✅ Successfully loaded {len(doc_ids)} documents")
                return True
            else:
                print("❌ Failed to load documents")
                return False
                
        except FileNotFoundError:
            print(f"❌ File not found: {file_path}")
            return False
        except json.JSONDecodeError:
            print(f"❌ Invalid JSON in file: {file_path}")
            return False
        except Exception as e:
            print(f"❌ Error loading from file: {e}")
            return False
    
    def get_database_stats(self):
        """Get and display database statistics"""
        try:
            stats = self.chatbot.get_vector_store_stats()
            print("\n📊 Database Statistics:")
            print(f"Total vectors: {stats.get('total_vector_count', 'Unknown')}")
            print(f"Index fullness: {stats.get('index_fullness', 'Unknown')}")
            print(f"Dimension: {stats.get('dimension', 'Unknown')}")
            
            if 'namespaces' in stats:
                print("Namespaces:")
                for namespace, info in stats['namespaces'].items():
                    print(f"  - {namespace}: {info.get('vector_count', 0)} vectors")
                    
        except Exception as e:
            print(f"❌ Error getting database stats: {e}")

def main():
    """Main function to load sample data"""
    print("🚀 Loading sample data into Thai-English Chatbot...")
    print("=" * 50)
    
    loader = DataLoader()
    
    # Load sample data
    if loader.load_sample_data():
        print("\n✅ Sample data loaded successfully!")
        
        # Show database stats
        loader.get_database_stats()
        
        print("\n🎉 Data loading completed!")
        print("You can now run the chatbot with: streamlit run streamlit_app.py")
    else:
        print("\n❌ Failed to load sample data")
        print("Please check your configuration and try again")

if __name__ == "__main__":
    main()
