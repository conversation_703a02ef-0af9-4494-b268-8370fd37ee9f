#!/usr/bin/env python3
"""
Setup script for Thai-English Chatbot
This script helps initialize the project and check dependencies
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version}")
    return True

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def setup_environment():
    """Set up environment file"""
    env_example = Path(".env.example")
    env_file = Path(".env")
    
    if not env_file.exists():
        if env_example.exists():
            # Copy example to .env
            with open(env_example, 'r') as src, open(env_file, 'w') as dst:
                dst.write(src.read())
            print("✅ Created .env file from template")
            print("⚠️  Please edit .env file and add your API keys")
        else:
            print("❌ .env.example file not found")
            return False
    else:
        print("✅ .env file already exists")
    
    return True

def check_api_keys():
    """Check if API keys are configured"""
    from dotenv import load_dotenv
    load_dotenv()
    
    required_keys = [
        "OPENAI_API_KEY",
        "PINECONE_API_KEY", 
        "PINECONE_ENVIRONMENT"
    ]
    
    missing_keys = []
    for key in required_keys:
        if not os.getenv(key):
            missing_keys.append(key)
    
    if missing_keys:
        print(f"⚠️  Missing API keys: {', '.join(missing_keys)}")
        print("Please add them to your .env file")
        return False
    
    print("✅ All required API keys are configured")
    return True

def test_imports():
    """Test if all required modules can be imported"""
    print("🧪 Testing imports...")
    
    required_modules = [
        "streamlit",
        "openai", 
        "pinecone",
        "langdetect",
        "sentence_transformers",
        "python-dotenv"
    ]
    
    failed_imports = []
    for module in required_modules:
        try:
            if module == "python-dotenv":
                import dotenv
            else:
                __import__(module.replace("-", "_"))
            print(f"  ✅ {module}")
        except ImportError:
            print(f"  ❌ {module}")
            failed_imports.append(module)
    
    if failed_imports:
        print(f"❌ Failed to import: {', '.join(failed_imports)}")
        return False
    
    print("✅ All modules imported successfully")
    return True

def create_sample_data():
    """Create sample data for testing"""
    print("📝 Creating sample data...")
    
    sample_data = [
        {
            "text": "Bangkok is the capital and largest city of Thailand. It's known for its vibrant street life, cultural landmarks, and delicious street food.",
            "metadata": {
                "language": "en",
                "category": "geography",
                "source": "sample_data"
            }
        },
        {
            "text": "กรุงเทพมหานครเป็นเมืองหลวงและเมืองที่ใหญ่ที่สุดของประเทศไทย มีชื่อเสียงในเรื่องวิถีชีวิตที่คึกคัก สถานที่สำคัญทางวัฒนธรรม และอาหารริมทางที่อร่อย",
            "metadata": {
                "language": "th", 
                "category": "geography",
                "source": "sample_data"
            }
        },
        {
            "text": "Thai cuisine is characterized by its balance of sweet, sour, salty, and spicy flavors. Popular dishes include Pad Thai, Tom Yum soup, and Green Curry.",
            "metadata": {
                "language": "en",
                "category": "food",
                "source": "sample_data"
            }
        },
        {
            "text": "อาหารไทยมีเอกลักษณ์ที่การผสมผสานรสหวาน เปรียว เค็ม และเผ็ด อาหารยอดนิยม ได้แก่ ผัดไทย ต้มยำ และแกงเขียวหวาน",
            "metadata": {
                "language": "th",
                "category": "food", 
                "source": "sample_data"
            }
        }
    ]
    
    # Save sample data to file
    import json
    with open("sample_data.json", "w", encoding="utf-8") as f:
        json.dump(sample_data, f, ensure_ascii=False, indent=2)
    
    print("✅ Sample data created in sample_data.json")
    return True

def main():
    """Main setup function"""
    print("🚀 Setting up Thai-English Chatbot...")
    print("=" * 50)
    
    steps = [
        ("Checking Python version", check_python_version),
        ("Installing dependencies", install_dependencies),
        ("Setting up environment", setup_environment),
        ("Testing imports", test_imports),
        ("Creating sample data", create_sample_data),
    ]
    
    for step_name, step_func in steps:
        print(f"\n{step_name}...")
        if not step_func():
            print(f"❌ Setup failed at: {step_name}")
            sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Edit .env file and add your API keys")
    print("2. Run: streamlit run streamlit_app.py")
    print("3. Open http://localhost:8501 in your browser")
    
    # Check API keys as final step
    print("\n🔑 Checking API keys...")
    if check_api_keys():
        print("✅ Ready to run the application!")
    else:
        print("⚠️  Please configure your API keys before running the app")

if __name__ == "__main__":
    main()
