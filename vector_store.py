from pinecone import Pinecone, ServerlessSpec
from sentence_transformers import SentenceTransformer
import numpy as np
from typing import List, Dict, Any, Optional
import hashlib
import json
from config import Config

class VectorStore:
    def __init__(self):
        self.config = Config()
        self.embedding_model = None
        self.pc = None
        self.index = None
        self._initialize_pinecone()
        self._initialize_embedding_model()

    def _initialize_pinecone(self):
        """Initialize Pinecone client and index"""
        try:
            # Initialize Pinecone client
            self.pc = Pinecone(api_key=self.config.PINECONE_API_KEY)

            # Check if index exists, create if not
            existing_indexes = [index.name for index in self.pc.list_indexes()]

            if self.config.PINECONE_INDEX_NAME not in existing_indexes:
                self.pc.create_index(
                    name=self.config.PINECONE_INDEX_NAME,
                    dimension=self.config.EMBEDDING_DIMENSION,
                    metric="cosine",
                    spec=ServerlessSpec(
                        cloud="aws",
                        region="us-east-1"
                    )
                )

            self.index = self.pc.Index(self.config.PINECONE_INDEX_NAME)
            print(f"Connected to Pinecone index: {self.config.PINECONE_INDEX_NAME}")

        except Exception as e:
            print(f"Error initializing Pinecone: {e}")
            raise
    
    def _initialize_embedding_model(self):
        """Initialize the sentence transformer model"""
        try:
            self.embedding_model = SentenceTransformer(self.config.EMBEDDING_MODEL)
            print(f"Loaded embedding model: {self.config.EMBEDDING_MODEL}")
        except Exception as e:
            print(f"Error loading embedding model: {e}")
            raise
    
    def create_embedding(self, text: str) -> List[float]:
        """Create embedding for given text"""
        if not self.embedding_model:
            raise ValueError("Embedding model not initialized")
        
        embedding = self.embedding_model.encode(text)
        return embedding.tolist()
    
    def add_document(self, text: str, metadata: Dict[str, Any]) -> str:
        """
        Add a document to the vector store
        
        Args:
            text (str): Document text
            metadata (dict): Document metadata (language, category, etc.)
            
        Returns:
            str: Document ID
        """
        # Create embedding
        embedding = self.create_embedding(text)
        
        # Generate unique ID
        doc_id = self._generate_doc_id(text, metadata)
        
        # Prepare metadata
        full_metadata = {
            **metadata,
            'text': text,
            'text_length': len(text)
        }
        
        # Upsert to Pinecone
        self.index.upsert(
            vectors=[(doc_id, embedding, full_metadata)]
        )
        
        return doc_id
    
    def add_documents_batch(self, documents: List[Dict[str, Any]]) -> List[str]:
        """
        Add multiple documents in batch
        
        Args:
            documents: List of dicts with 'text' and 'metadata' keys
            
        Returns:
            List of document IDs
        """
        vectors = []
        doc_ids = []
        
        for doc in documents:
            text = doc['text']
            metadata = doc.get('metadata', {})
            
            embedding = self.create_embedding(text)
            doc_id = self._generate_doc_id(text, metadata)
            
            full_metadata = {
                **metadata,
                'text': text,
                'text_length': len(text)
            }
            
            vectors.append((doc_id, embedding, full_metadata))
            doc_ids.append(doc_id)
        
        # Batch upsert
        self.index.upsert(vectors=vectors)
        
        return doc_ids
    
    def search(self, query: str, top_k: int = None, filter_dict: Dict = None) -> List[Dict[str, Any]]:
        """
        Search for similar documents
        
        Args:
            query (str): Search query
            top_k (int): Number of results to return
            filter_dict (dict): Metadata filters
            
        Returns:
            List of matching documents with scores
        """
        if top_k is None:
            top_k = self.config.TOP_K_RESULTS
        
        # Create query embedding
        query_embedding = self.create_embedding(query)
        
        # Search in Pinecone
        search_results = self.index.query(
            vector=query_embedding,
            top_k=top_k,
            include_metadata=True,
            filter=filter_dict
        )
        
        # Format results
        results = []
        for match in search_results['matches']:
            if match['score'] >= self.config.SIMILARITY_THRESHOLD:
                results.append({
                    'id': match['id'],
                    'score': match['score'],
                    'text': match['metadata'].get('text', ''),
                    'metadata': {k: v for k, v in match['metadata'].items() if k != 'text'}
                })
        
        return results
    
    def search_by_language(self, query: str, language: str, top_k: int = None) -> List[Dict[str, Any]]:
        """Search for documents in a specific language"""
        filter_dict = {"language": {"$eq": language}}
        return self.search(query, top_k, filter_dict)
    
    def delete_document(self, doc_id: str) -> bool:
        """Delete a document by ID"""
        try:
            self.index.delete(ids=[doc_id])
            return True
        except Exception as e:
            print(f"Error deleting document {doc_id}: {e}")
            return False
    
    def get_index_stats(self) -> Dict[str, Any]:
        """Get index statistics"""
        try:
            stats = self.index.describe_index_stats()
            return stats
        except Exception as e:
            print(f"Error getting index stats: {e}")
            return {}
    
    def _generate_doc_id(self, text: str, metadata: Dict[str, Any]) -> str:
        """Generate a unique document ID"""
        content = f"{text}_{json.dumps(metadata, sort_keys=True)}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def clear_index(self) -> bool:
        """Clear all documents from the index (use with caution)"""
        try:
            self.index.delete(delete_all=True)
            return True
        except Exception as e:
            print(f"Error clearing index: {e}")
            return False
