# Thai-English Chatbot with RAG

A multilingual AI chatbot built with Streamlit that supports both English and Thai languages, featuring intelligent language detection, need identification, and RAG (Retrieval-Augmented Generation) capabilities using Pinecone vector database.

## Features

- 🌐 **Multilingual Support**: Automatic language detection for English and Thai
- 🧠 **Smart AI Responses**: Powered by OpenAI GPT models
- 📚 **Knowledge Base**: RAG implementation with Pinecone vector database
- 🔍 **Need Identification**: Automatically identifies user intent and requirements
- 💬 **Interactive UI**: Clean and responsive Streamlit interface
- 📊 **Conversation Analytics**: Track usage statistics and conversation metrics

## Prerequisites

- Python 3.8 or higher
- OpenAI API key
- Pinecone API key and environment

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd ThaiChatbot
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` file and add your API keys:
   ```
   OPENAI_API_KEY=your_openai_api_key_here
   PINECONE_API_KEY=your_pinecone_api_key_here
   PINECONE_ENVIRONMENT=your_pinecone_environment_here
   PINECONE_INDEX_NAME=thai-chatbot-index
   ```

## Usage

1. **Run the Streamlit app**:
   ```bash
   streamlit run streamlit_app.py
   ```

2. **Access the application**:
   Open your browser and go to `http://localhost:8501`

3. **Start chatting**:
   - Type messages in English or Thai
   - The system will automatically detect the language
   - Enable/disable knowledge base search in the sidebar
   - Adjust model parameters as needed

## Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `OPENAI_API_KEY` | Your OpenAI API key | Yes |
| `PINECONE_API_KEY` | Your Pinecone API key | Yes |
| `PINECONE_ENVIRONMENT` | Pinecone environment (e.g., 'us-west1-gcp') | Yes |
| `PINECONE_INDEX_NAME` | Name for your Pinecone index | No (default: 'thai-chatbot-index') |
| `APP_TITLE` | Application title | No (default: 'Thai-English Chatbot') |

### Model Configuration

You can adjust these settings in `config.py`:

- `OPENAI_MODEL`: GPT model to use (default: 'gpt-3.5-turbo')
- `EMBEDDING_MODEL`: Sentence transformer model for embeddings
- `MAX_TOKENS`: Maximum tokens per response
- `TEMPERATURE`: Response creativity (0.0-1.0)
- `TOP_K_RESULTS`: Number of knowledge base results to retrieve

## Adding Knowledge to the Database

You can add documents to the knowledge base programmatically:

```python
from chatbot import ThaiChatbot

chatbot = ThaiChatbot()

# Add a single document
chatbot.add_knowledge(
    text="Bangkok is the capital city of Thailand.",
    metadata={
        "language": "en",
        "category": "geography",
        "source": "general_knowledge"
    }
)

# Add multiple documents
documents = [
    {
        "text": "กรุงเทพมหานครเป็นเมืองหลวงของประเทศไทย",
        "metadata": {"language": "th", "category": "geography"}
    },
    {
        "text": "Thai cuisine is known for its balance of sweet, sour, salty, and spicy flavors.",
        "metadata": {"language": "en", "category": "culture"}
    }
]
chatbot.add_knowledge_batch(documents)
```

## Project Structure

```
ThaiChatbot/
├── streamlit_app.py      # Main Streamlit application
├── chatbot.py           # Core chatbot logic
├── config.py            # Configuration settings
├── language_detector.py # Language detection module
├── vector_store.py      # Pinecone vector database integration
├── llm_client.py        # OpenAI LLM client
├── requirements.txt     # Python dependencies
├── .env.example        # Environment variables template
└── README.md           # This file
```

## API Keys Setup

### OpenAI API Key
1. Go to [OpenAI API](https://platform.openai.com/api-keys)
2. Create a new API key
3. Add it to your `.env` file

### Pinecone API Key
1. Sign up at [Pinecone](https://www.pinecone.io/)
2. Create a new project
3. Get your API key and environment from the dashboard
4. Add them to your `.env` file

## Troubleshooting

### Common Issues

1. **"Configuration Error: Missing required environment variables"**
   - Make sure your `.env` file exists and contains all required variables
   - Check that your API keys are valid

2. **"Error initializing Pinecone"**
   - Verify your Pinecone API key and environment
   - Check if you have sufficient quota in your Pinecone account

3. **"Error loading embedding model"**
   - Ensure you have internet connection for downloading the model
   - Try running `pip install sentence-transformers` separately

### Performance Tips

- For better performance, consider using a more powerful embedding model
- Adjust `TOP_K_RESULTS` based on your knowledge base size
- Use batch operations when adding multiple documents

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Check the troubleshooting section above
- Review the configuration settings
