import openai
from typing import List, Dict, Any, Optional
from config import Config
import json

class LLMClient:
    def __init__(self):
        self.config = Config()
        openai.api_key = self.config.OPENAI_API_KEY
        
        # System prompts for different scenarios
        self.system_prompts = {
            'general': """You are a helpful multilingual AI assistant that can communicate in both English and Thai. 
You should respond in the same language as the user's input unless specifically asked to translate.
Be helpful, accurate, and culturally sensitive when responding to questions in either language.
If you receive context from a knowledge base, use it to provide more accurate and relevant responses.""",
            
            'thai_specialist': """คุณเป็นผู้ช่วย AI ที่สามารถสื่อสารได้ทั้งภาษาไทยและภาษาอังกฤษ 
คุณควรตอบในภาษาเดียวกับที่ผู้ใช้ถาม เว้นแต่จะถูกขอให้แปลภาษา
จงให้ความช่วยเหลือ ตอบอย่างถูกต้อง และคำนึงถึงวัฒนธรรมเมื่อตอบคำถามในภาษาใดภาษาหนึ่ง
หากคุณได้รับบริบทจากฐานความรู้ ให้ใช้ข้อมูลนั้นเพื่อให้คำตอบที่แม่นยำและเกี่ยวข้องมากขึ้น""",
            
            'need_identification': """You are an AI assistant specialized in identifying user needs and intent.
Analyze the user's message and identify:
1. The main topic or subject they're asking about
2. The type of help they need (information, advice, problem-solving, etc.)
3. The urgency or priority level
4. Any specific requirements or constraints mentioned

Respond in the same language as the user's input."""
        }
    
    def generate_response(
        self, 
        user_message: str, 
        language: str = 'auto',
        context: Optional[List[Dict[str, Any]]] = None,
        conversation_history: Optional[List[Dict[str, str]]] = None,
        system_prompt_type: str = 'general'
    ) -> Dict[str, Any]:
        """
        Generate a response using the LLM
        
        Args:
            user_message (str): User's input message
            language (str): Detected language ('en', 'th', or 'auto')
            context (List[Dict]): RAG context from vector store
            conversation_history (List[Dict]): Previous conversation messages
            system_prompt_type (str): Type of system prompt to use
            
        Returns:
            Dict containing response and metadata
        """
        try:
            # Select appropriate system prompt
            system_prompt = self._get_system_prompt(language, system_prompt_type)
            
            # Build messages
            messages = [{"role": "system", "content": system_prompt}]
            
            # Add conversation history if provided
            if conversation_history:
                messages.extend(conversation_history[-10:])  # Keep last 10 messages
            
            # Add context if provided
            if context:
                context_text = self._format_context(context, language)
                context_message = f"Context from knowledge base:\n{context_text}\n\nUser question: {user_message}"
                messages.append({"role": "user", "content": context_message})
            else:
                messages.append({"role": "user", "content": user_message})
            
            # Generate response
            response = openai.ChatCompletion.create(
                model=self.config.OPENAI_MODEL,
                messages=messages,
                max_tokens=self.config.MAX_TOKENS,
                temperature=self.config.TEMPERATURE,
                top_p=1,
                frequency_penalty=0,
                presence_penalty=0
            )
            
            assistant_message = response.choices[0].message.content.strip()
            
            return {
                'response': assistant_message,
                'model': self.config.OPENAI_MODEL,
                'tokens_used': response.usage.total_tokens,
                'context_used': len(context) if context else 0,
                'language': language
            }
            
        except Exception as e:
            return {
                'response': self._get_error_message(language),
                'error': str(e),
                'model': self.config.OPENAI_MODEL,
                'tokens_used': 0,
                'context_used': 0,
                'language': language
            }
    
    def identify_user_needs(self, user_message: str, language: str) -> Dict[str, Any]:
        """
        Identify user needs and intent from their message
        
        Args:
            user_message (str): User's input message
            language (str): Detected language
            
        Returns:
            Dict containing identified needs and categories
        """
        try:
            prompt = self._build_need_identification_prompt(user_message, language)
            
            response = openai.ChatCompletion.create(
                model=self.config.OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": self.system_prompts['need_identification']},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=300,
                temperature=0.3
            )
            
            # Try to parse as JSON, fallback to text
            response_text = response.choices[0].message.content.strip()
            try:
                needs_analysis = json.loads(response_text)
            except json.JSONDecodeError:
                needs_analysis = {'analysis': response_text, 'category': 'general'}
            
            return needs_analysis
            
        except Exception as e:
            return {
                'error': str(e),
                'category': 'general',
                'analysis': 'Unable to analyze user needs'
            }
    
    def _get_system_prompt(self, language: str, prompt_type: str) -> str:
        """Get appropriate system prompt based on language and type"""
        if language == 'th' and prompt_type == 'general':
            return self.system_prompts['thai_specialist']
        return self.system_prompts.get(prompt_type, self.system_prompts['general'])
    
    def _format_context(self, context: List[Dict[str, Any]], language: str) -> str:
        """Format RAG context for inclusion in prompt"""
        if not context:
            return ""
        
        formatted_context = []
        for i, item in enumerate(context[:3], 1):  # Use top 3 results
            text = item.get('text', '')
            score = item.get('score', 0)
            formatted_context.append(f"[Context {i}] (Relevance: {score:.2f})\n{text}")
        
        return "\n\n".join(formatted_context)
    
    def _build_need_identification_prompt(self, user_message: str, language: str) -> str:
        """Build prompt for need identification"""
        if language == 'th':
            return f"""วิเคราะห์ข้อความต่อไปนี้และระบุความต้องการของผู้ใช้:

ข้อความ: "{user_message}"

กรุณาตอบในรูปแบบ JSON ที่มีฟิลด์ต่อไปนี้:
- "category": หมวดหมู่หลัก (เช่น "ข้อมูล", "คำแนะนำ", "การแก้ปัญหา", "การสนทนา")
- "topic": หัวข้อหลักที่ถาม
- "urgency": ระดับความเร่งด่วน ("สูง", "กลาง", "ต่ำ")
- "intent": เจตนาหลักของผู้ใช้
- "requirements": ข้อกำหนดหรือข้อจำกัดที่กล่าวถึง"""
        else:
            return f"""Analyze the following message and identify the user's needs:

Message: "{user_message}"

Please respond in JSON format with the following fields:
- "category": Main category (e.g., "information", "advice", "problem-solving", "conversation")
- "topic": Main topic being asked about
- "urgency": Urgency level ("high", "medium", "low")
- "intent": Primary user intent
- "requirements": Any specific requirements or constraints mentioned"""
    
    def _get_error_message(self, language: str) -> str:
        """Get error message in appropriate language"""
        if language == 'th':
            return "ขออภัย เกิดข้อผิดพลาดในการประมวลผลคำถามของคุณ กรุณาลองใหม่อีกครั้ง"
        else:
            return "I apologize, but I encountered an error processing your question. Please try again."
