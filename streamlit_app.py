import streamlit as st
import time
from datetime import datetime
from typing import List, Dict, Any
import json

# Import our custom modules
from config import Config
from language_detector import LanguageDetector
from vector_store import VectorStore
from llm_client import LLMClient
from chatbot import ThaiChatbot

# Page configuration
st.set_page_config(
    page_title="Thai-English Chatbot",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        text-align: center;
        padding: 1rem 0;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        margin-bottom: 2rem;
    }
    
    .chat-message {
        padding: 1rem;
        border-radius: 10px;
        margin: 0.5rem 0;
        border-left: 4px solid #667eea;
    }
    
    .user-message {
        background-color: #f0f2f6;
        border-left-color: #667eea;
    }
    
    .assistant-message {
        background-color: #e8f4fd;
        border-left-color: #1f77b4;
    }
    
    .language-badge {
        display: inline-block;
        padding: 0.2rem 0.5rem;
        background-color: #667eea;
        color: white;
        border-radius: 15px;
        font-size: 0.8rem;
        margin-left: 0.5rem;
    }
    
    .stats-container {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 10px;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

class StreamlitUI:
    def __init__(self):
        self.config = Config()
        self.initialize_session_state()
        
    def initialize_session_state(self):
        """Initialize Streamlit session state variables"""
        if 'messages' not in st.session_state:
            st.session_state.messages = []
        
        if 'chatbot' not in st.session_state:
            try:
                st.session_state.chatbot = ThaiChatbot()
            except Exception as e:
                st.error(f"Failed to initialize chatbot: {e}")
                st.stop()
        
        if 'conversation_stats' not in st.session_state:
            st.session_state.conversation_stats = {
                'total_messages': 0,
                'languages_detected': set(),
                'total_tokens_used': 0
            }
    
    def render_header(self):
        """Render the main header"""
        st.markdown(f"""
        <div class="main-header">
            <h1>🤖 {self.config.APP_TITLE}</h1>
            <p>Multilingual AI Assistant supporting English and Thai</p>
        </div>
        """, unsafe_allow_html=True)
    
    def render_sidebar(self):
        """Render the sidebar with settings and stats"""
        with st.sidebar:
            st.header("⚙️ Settings")
            
            # Language preference
            language_preference = st.selectbox(
                "Language Preference",
                ["auto", "en", "th"],
                format_func=lambda x: {
                    "auto": "🌐 Auto-detect",
                    "en": "🇺🇸 English",
                    "th": "🇹🇭 Thai"
                }[x]
            )
            
            # RAG settings
            st.subheader("🔍 Search Settings")
            use_rag = st.checkbox("Enable Knowledge Base Search", value=True)
            top_k_results = st.slider("Max Search Results", 1, 10, 5)
            
            # Model settings
            st.subheader("🧠 Model Settings")
            temperature = st.slider("Response Creativity", 0.0, 1.0, 0.7, 0.1)
            max_tokens = st.slider("Max Response Length", 100, 2000, 1000, 100)
            
            # Update config
            st.session_state.chatbot.config.TEMPERATURE = temperature
            st.session_state.chatbot.config.MAX_TOKENS = max_tokens
            st.session_state.chatbot.config.TOP_K_RESULTS = top_k_results
            
            # Conversation stats
            st.subheader("📊 Conversation Stats")
            stats = st.session_state.conversation_stats

            # Convert languages set to list for display
            languages_list = list(stats['languages_detected']) if stats['languages_detected'] else []
            languages_display = ', '.join(languages_list) if languages_list else 'None'

            # Use st.metric for better real-time updates
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Messages", stats['total_messages'])
            with col2:
                st.metric("Languages", len(languages_list))
            with col3:
                st.metric("Tokens", stats['total_tokens_used'])

            if languages_list:
                st.write(f"**Languages Used:** {languages_display}")
            
            # Clear conversation
            if st.button("🗑️ Clear Conversation"):
                st.session_state.messages = []
                st.session_state.conversation_stats = {
                    'total_messages': 0,
                    'languages_detected': set(),
                    'total_tokens_used': 0
                }
                st.rerun()
            
            return {
                'language_preference': language_preference,
                'use_rag': use_rag,
                'top_k_results': top_k_results
            }
    
    def render_chat_history(self):
        """Render the chat message history"""
        for message in st.session_state.messages:
            with st.chat_message(message["role"]):
                # Display message content
                st.write(message["content"])
                
                # Display metadata if available
                if "metadata" in message:
                    metadata = message["metadata"]
                    
                    # Language detection info
                    if "language" in metadata:
                        lang_info = metadata["language"]
                        st.markdown(f"""
                        <span class="language-badge">
                            {lang_info.get('name', 'Unknown')} 
                            ({lang_info.get('confidence', 0):.1%})
                        </span>
                        """, unsafe_allow_html=True)
                    
                    # Show context used (for assistant messages)
                    if message["role"] == "assistant" and "context_used" in metadata:
                        if metadata["context_used"] > 0:
                            st.caption(f"📚 Used {metadata['context_used']} knowledge base entries")
                    
                    # Show tokens used
                    if "tokens_used" in metadata and metadata["tokens_used"] > 0:
                        st.caption(f"🔢 Tokens: {metadata['tokens_used']}")
    
    def handle_user_input(self, settings: Dict[str, Any]):
        """Handle user input and generate response"""
        if prompt := st.chat_input("Type your message here... / พิมพ์ข้อความที่นี่..."):
            # Add user message to chat history
            st.session_state.messages.append({
                "role": "user",
                "content": prompt,
                "timestamp": datetime.now().isoformat()
            })
            
            # Display user message
            with st.chat_message("user"):
                st.write(prompt)
            
            # Generate and display assistant response
            with st.chat_message("assistant"):
                with st.spinner("Thinking... / กำลังคิด..."):
                    response_data = st.session_state.chatbot.process_message(
                        user_message=prompt,
                        use_rag=settings['use_rag'],
                        language_preference=settings['language_preference']
                    )
                
                # Display response
                st.write(response_data['response'])
                
                # Add assistant message to chat history
                st.session_state.messages.append({
                    "role": "assistant",
                    "content": response_data['response'],
                    "metadata": response_data,
                    "timestamp": datetime.now().isoformat()
                })
                
                # Update conversation stats
                self.update_conversation_stats(response_data)
    
    def update_conversation_stats(self, response_data: Dict[str, Any]):
        """Update conversation statistics"""
        stats = st.session_state.conversation_stats
        stats['total_messages'] += 2  # User + Assistant

        if 'language_detected' in response_data:
            lang_code = response_data['language_detected'].get('code')
            if lang_code:
                stats['languages_detected'].add(lang_code)

        if 'tokens_used' in response_data:
            stats['total_tokens_used'] += response_data['tokens_used']
    
    def render_welcome_message(self):
        """Render welcome message if no conversation exists"""
        if not st.session_state.messages:
            st.markdown("""
            ### 👋 Welcome! / ยินดีต้อนรับ!
            
            I'm your multilingual AI assistant. I can help you with:
            - **Questions in English or Thai** / คำถามในภาษาอังกฤษหรือไทย
            - **Information retrieval** / การค้นหาข้อมูล  
            - **Problem solving** / การแก้ปัญหา
            - **General conversation** / การสนทนาทั่วไป
            
            Just type your message below to get started!
            เพียงพิมพ์ข้อความด้านล่างเพื่อเริ่มต้น!
            """)
    
    def run(self):
        """Main application runner"""
        # Render header
        self.render_header()
        
        # Render sidebar and get settings
        settings = self.render_sidebar()
        
        # Main chat interface
        col1, col2 = st.columns([3, 1])
        
        with col1:
            # Welcome message or chat history
            if st.session_state.messages:
                self.render_chat_history()
            else:
                self.render_welcome_message()
            
            # Handle user input
            self.handle_user_input(settings)
        
        with col2:
            # Additional info panel (could be expanded)
            st.subheader("ℹ️ About")
            st.markdown("""
            This chatbot uses:
            - 🧠 OpenAI GPT for responses
            - 🔍 Pinecone for knowledge base
            - 🌐 Language detection
            - 📚 RAG for context-aware answers
            """)

# Main execution
if __name__ == "__main__":
    try:
        # Validate configuration
        Config.validate_config()
        
        # Run the app
        app = StreamlitUI()
        app.run()
        
    except ValueError as e:
        st.error(f"Configuration Error: {e}")
        st.info("Please check your .env file and ensure all required API keys are set.")
    except Exception as e:
        st.error(f"Application Error: {e}")
        st.info("Please check the logs for more details.")
